"""
Advanced Plotly + Dash Interactive Data Visualizer
Creates a sophisticated interactive visualization with multiple chart types and detailed drill-down capabilities.
"""

import json
import sys
import dash
from dash import dcc, html, Input, Output, State, callback_context
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime
import math


class PlotlyDashVisualizer:
    def __init__(self, json_file_path: str):
        """Initialize the visualizer with JSON data."""
        self.json_file_path = json_file_path
        print(f"📁 Loading data from: {json_file_path}")

        with open(json_file_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)

        # Print some debug info
        user = self.data.get('User', {})
        user_name = f"{user.get('Firstname', '')} {user.get('Lastname', '')}"
        print(f"👤 Loaded user: {user_name}")
        print(f"📊 Appointments: {len(self.data.get('Appointments', []))}")

        # Create unique app instance to avoid caching issues
        import time
        app_name = f"plotly_dash_visualizer_{int(time.time())}"
        self.app = dash.Dash(app_name)

        # Disable caching
        self.app.config.suppress_callback_exceptions = True

        self.node_info = {}
        self.setup_layout()
        self.setup_callbacks()
    
    def format_datetime(self, dt_str):
        """Format datetime strings for display."""
        if not dt_str:
            return "N/A"
        try:
            if isinstance(dt_str, str):
                dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
                return dt.strftime("%d-%b-%Y %H:%M")
            return str(dt_str)
        except:
            return str(dt_str)
    
    def create_network_graph(self):
        """Create an interactive network graph visualization."""
        fig = go.Figure()
        
        # Extract user info
        user = self.data.get('User', {})
        user_name = f"{user.get('Firstname', '')} {user.get('Lastname', '')}"
        
        # Create nodes and edges
        nodes = []
        edges = []
        
        # Root node - User
        user_id = f"user_{user.get('UserID', 0)}"
        nodes.append({
            'id': user_id,
            'label': f"👤 {user_name}",
            'x': 0, 'y': 0,
            'color': '#FF6B6B',
            'size': 40,
            'type': 'user',
            'hover_text': f"<b>User: {user_name}</b><br>UserID: {user.get('UserID', 'N/A')}<br>Email: {user.get('Email', 'N/A')}"
        })
        self.node_info[user_id] = {'type': 'user', 'data': user}
        
        # Appointments in a circle around user
        appointments = self.data.get('Appointments', [])
        n_appts = len(appointments)
        
        for i, appt_data in enumerate(appointments):
            appt = appt_data.get('Appointment', {})
            appt_id = f"appt_{appt.get('AppointmentId', i)}"
            appt_no = appt.get('AppNo', 'N/A')
            
            # Position appointments in a circle
            angle = 2 * math.pi * i / n_appts if n_appts > 1 else 0
            radius = 3
            x = radius * math.cos(angle)
            y = radius * math.sin(angle)
            
            # Count related data
            n_support = len(appt_data.get('Appointments_Support', []))
            n_outbound = len(appt_data.get('OutBound_Document', []))
            n_workflows = len(appt_data.get('Workflows', []))
            
            nodes.append({
                'id': appt_id,
                'label': f"📅 {appt_no}",
                'x': x, 'y': y,
                'color': '#4ECDC4',
                'size': 25 + min(n_support + n_outbound + n_workflows, 15),  # Size based on data volume
                'type': 'appointment',
                'hover_text': f"<b>Appointment {appt_no}</b><br>Support: {n_support}<br>OutBound: {n_outbound}<br>Workflows: {n_workflows}"
            })
            self.node_info[appt_id] = {'type': 'appointment', 'data': appt_data}
            edges.append((user_id, appt_id))
            
            # Workflows around each appointment
            workflows = appt_data.get('Workflows', [])
            for j, workflow in enumerate(workflows):
                wf_id = f"wf_{appt_id}_{j}"
                wf_appt_id = workflow.get('AppointmentId', 'N/A')
                
                # Position workflows around appointments
                wf_angle = angle + (j - len(workflows)/2) * 0.5
                wf_radius = radius + 1.5
                wf_x = wf_radius * math.cos(wf_angle)
                wf_y = wf_radius * math.sin(wf_angle)
                
                n_audits = len(workflow.get('WorkflowAuditLog', []))
                n_kiosks = len(workflow.get('KioskWorkflow', []))
                
                nodes.append({
                    'id': wf_id,
                    'label': f"⚙️ WF{wf_appt_id}",
                    'x': wf_x, 'y': wf_y,
                    'color': '#45B7D1',
                    'size': 15 + min(n_audits + n_kiosks, 10),
                    'type': 'workflow',
                    'hover_text': f"<b>Workflow {wf_appt_id}</b><br>Audits: {n_audits}<br>Kiosks: {n_kiosks}"
                })
                self.node_info[wf_id] = {'type': 'workflow', 'data': workflow}
                edges.append((appt_id, wf_id))
        
        return self.create_plotly_network(nodes, edges)
    
    def create_plotly_network(self, nodes, edges):
        """Create the Plotly network visualization."""
        # Create edge traces
        edge_x = []
        edge_y = []
        for edge in edges:
            start_node = next(n for n in nodes if n['id'] == edge[0])
            end_node = next(n for n in nodes if n['id'] == edge[1])
            edge_x.extend([start_node['x'], end_node['x'], None])
            edge_y.extend([start_node['y'], end_node['y'], None])
        
        fig = go.Figure()
        
        # Add edges
        fig.add_trace(go.Scatter(
            x=edge_x, y=edge_y,
            mode='lines',
            line=dict(width=2, color='rgba(125,125,125,0.5)'),
            hoverinfo='none',
            showlegend=False,
            name='connections'
        ))
        
        # Add nodes
        fig.add_trace(go.Scatter(
            x=[n['x'] for n in nodes],
            y=[n['y'] for n in nodes],
            mode='markers+text',
            marker=dict(
                size=[n['size'] for n in nodes],
                color=[n['color'] for n in nodes],
                line=dict(width=3, color='white'),
                opacity=0.9
            ),
            text=[n['label'] for n in nodes],
            textposition="middle center",
            textfont=dict(size=10, color='white'),
            customdata=[n['id'] for n in nodes],
            hovertemplate='%{hovertext}<br><i>Click for details</i><extra></extra>',
            hovertext=[n['hover_text'] for n in nodes],
            showlegend=False,
            name='nodes'
        ))
        
        fig.update_layout(
            title={
                'text': "🌐 Interactive Data Network - Click nodes for detailed information",
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 20, 'color': '#2C3E50'}
            },
            showlegend=False,
            hovermode='closest',
            margin=dict(b=20,l=5,r=5,t=60),
            annotations=[
                dict(
                    text="💡 Click on any node to see detailed information | 🔍 Hover for quick stats",
                    showarrow=False,
                    xref="paper", yref="paper",
                    x=0.5, y=-0.05,
                    xanchor='center', yanchor='top',
                    font=dict(color='gray', size=12)
                )
            ],
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            plot_bgcolor='#F8F9FA',
            paper_bgcolor='white',
            height=600
        )
        
        return fig
    
    def create_summary_charts(self):
        """Create summary charts for the dashboard."""
        appointments = self.data.get('Appointments', [])
        
        # Data for charts
        appt_data = []
        for appt_info in appointments:
            appt = appt_info.get('Appointment', {})
            appt_data.append({
                'AppNo': appt.get('AppNo', 'N/A'),
                'Support_Count': len(appt_info.get('Appointments_Support', [])),
                'OutBound_Count': len(appt_info.get('OutBound_Document', [])),
                'Workflow_Count': len(appt_info.get('Workflows', [])),
                'Audit_Count': sum(len(wf.get('WorkflowAuditLog', [])) for wf in appt_info.get('Workflows', [])),
                'Kiosk_Count': sum(len(wf.get('KioskWorkflow', [])) for wf in appt_info.get('Workflows', []))
            })
        
        # Bar chart of data counts per appointment
        fig_bar = go.Figure()
        
        categories = ['Support_Count', 'OutBound_Count', 'Workflow_Count', 'Audit_Count', 'Kiosk_Count']
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
        names = ['Support Records', 'OutBound Docs', 'Workflows', 'Audit Logs', 'Kiosk Records']
        
        for i, (category, color, name) in enumerate(zip(categories, colors, names)):
            fig_bar.add_trace(go.Bar(
                name=name,
                x=[d['AppNo'] for d in appt_data],
                y=[d[category] for d in appt_data],
                marker_color=color,
                opacity=0.8
            ))
        
        fig_bar.update_layout(
            title="📊 Data Distribution Across Appointments",
            xaxis_title="Appointment Number",
            yaxis_title="Count",
            barmode='group',
            height=400,
            plot_bgcolor='#F8F9FA',
            paper_bgcolor='white'
        )
        
        # Pie chart of total data types
        totals = {
            'Appointments': len(appointments),
            'Support Records': sum(d['Support_Count'] for d in appt_data),
            'OutBound Docs': sum(d['OutBound_Count'] for d in appt_data),
            'Workflows': sum(d['Workflow_Count'] for d in appt_data),
            'Audit Logs': sum(d['Audit_Count'] for d in appt_data),
            'Kiosk Records': sum(d['Kiosk_Count'] for d in appt_data)
        }
        
        fig_pie = go.Figure(data=[go.Pie(
            labels=list(totals.keys()),
            values=list(totals.values()),
            hole=0.4,
            marker_colors=['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
        )])
        
        fig_pie.update_layout(
            title="🥧 Overall Data Distribution",
            height=400,
            plot_bgcolor='#F8F9FA',
            paper_bgcolor='white'
        )
        
        return fig_bar, fig_pie
    
    def setup_layout(self):
        """Setup the Dash app layout."""
        network_fig = self.create_network_graph()
        bar_fig, pie_fig = self.create_summary_charts()

        # Get summary stats
        appointments = self.data.get('Appointments', [])
        user = self.data.get('User', {})
        user_name = f"{user.get('Firstname', '')} {user.get('Lastname', '')}"

        # Create unique timestamp for cache busting
        import time
        timestamp = int(time.time())
        
        n_appt = len(appointments)
        n_sup = sum(len(a.get('Appointments_Support', [])) for a in appointments)
        n_outbound = sum(len(a.get('OutBound_Document', [])) for a in appointments)
        n_wf = sum(len(a.get('Workflows', [])) for a in appointments)
        n_audit = sum(sum(len(wf.get('WorkflowAuditLog', [])) for wf in a.get('Workflows', [])) for a in appointments)
        n_kiosk = sum(sum(len(wf.get('KioskWorkflow', [])) for wf in a.get('Workflows', [])) for a in appointments)
        
        self.app.layout = html.Div([
            # Header
            html.Div([
                html.H1("🎯 Advanced Interactive Data Visualizer",
                       style={'textAlign': 'center', 'color': '#2C3E50', 'marginBottom': 10}),
                html.H3(f"👤 User: {user_name}",
                       style={'textAlign': 'center', 'color': '#34495E', 'marginBottom': 10}),
                html.H4(f"📁 Data Source: {self.json_file_path}",
                       style={'textAlign': 'center', 'color': '#7F8C8D', 'marginBottom': 30, 'fontStyle': 'italic'})
            ]),
            
            # Summary metrics
            html.Div([
                html.Div([
                    html.H3(str(n_appt), style={'margin': 0, 'color': '#E74C3C', 'fontSize': 28}),
                    html.P("Appointments", style={'margin': 0, 'fontWeight': 'bold'})
                ], className='metric-card', style={'textAlign': 'center', 'padding': 20, 'backgroundColor': '#FFE5E5', 
                                                  'borderRadius': 10, 'margin': 10, 'flex': 1, 'border': '2px solid #E74C3C'}),
                
                html.Div([
                    html.H3(str(n_sup), style={'margin': 0, 'color': '#27AE60', 'fontSize': 28}),
                    html.P("Support Records", style={'margin': 0, 'fontWeight': 'bold'})
                ], className='metric-card', style={'textAlign': 'center', 'padding': 20, 'backgroundColor': '#E8F8F5', 
                                                  'borderRadius': 10, 'margin': 10, 'flex': 1, 'border': '2px solid #27AE60'}),
                
                html.Div([
                    html.H3(str(n_outbound), style={'margin': 0, 'color': '#F39C12', 'fontSize': 28}),
                    html.P("OutBound Docs", style={'margin': 0, 'fontWeight': 'bold'})
                ], className='metric-card', style={'textAlign': 'center', 'padding': 20, 'backgroundColor': '#FEF9E7', 
                                                  'borderRadius': 10, 'margin': 10, 'flex': 1, 'border': '2px solid #F39C12'}),
                
                html.Div([
                    html.H3(str(n_wf), style={'margin': 0, 'color': '#8E44AD', 'fontSize': 28}),
                    html.P("Workflows", style={'margin': 0, 'fontWeight': 'bold'})
                ], className='metric-card', style={'textAlign': 'center', 'padding': 20, 'backgroundColor': '#F4ECF7', 
                                                  'borderRadius': 10, 'margin': 10, 'flex': 1, 'border': '2px solid #8E44AD'}),
                
                html.Div([
                    html.H3(str(n_audit), style={'margin': 0, 'color': '#E67E22', 'fontSize': 28}),
                    html.P("Audit Logs", style={'margin': 0, 'fontWeight': 'bold'})
                ], className='metric-card', style={'textAlign': 'center', 'padding': 20, 'backgroundColor': '#FDF2E9', 
                                                  'borderRadius': 10, 'margin': 10, 'flex': 1, 'border': '2px solid #E67E22'}),
                
                html.Div([
                    html.H3(str(n_kiosk), style={'margin': 0, 'color': '#3498DB', 'fontSize': 28}),
                    html.P("Kiosk Records", style={'margin': 0, 'fontWeight': 'bold'})
                ], className='metric-card', style={'textAlign': 'center', 'padding': 20, 'backgroundColor': '#EBF5FB', 
                                                  'borderRadius': 10, 'margin': 10, 'flex': 1, 'border': '2px solid #3498DB'})
                
            ], style={'display': 'flex', 'flexWrap': 'wrap', 'marginBottom': 30}),
            
            # Main network graph
            dcc.Graph(
                id='network-graph',
                figure=network_fig,
                style={'marginBottom': 30}
            ),
            
            # Detail panel
            html.Div(id='detail-panel', 
                    children=self.get_default_details(),
                    style={'marginBottom': 30, 'padding': 30, 'backgroundColor': '#F8F9FA', 
                           'borderRadius': 15, 'border': '2px solid #BDC3C7', 'boxShadow': '0 4px 6px rgba(0,0,0,0.1)'}),
            
            # Charts section
            html.Div([
                html.H2("📈 Data Analytics Dashboard", 
                       style={'textAlign': 'center', 'color': '#2C3E50', 'marginBottom': 30}),
                
                html.Div([
                    html.Div([
                        dcc.Graph(figure=bar_fig)
                    ], style={'width': '60%', 'display': 'inline-block', 'verticalAlign': 'top'}),
                    
                    html.Div([
                        dcc.Graph(figure=pie_fig)
                    ], style={'width': '40%', 'display': 'inline-block', 'verticalAlign': 'top'})
                ])
            ])
            
        ], style={'padding': 30, 'fontFamily': 'Arial, sans-serif', 'backgroundColor': '#FFFFFF'})
    
    def setup_callbacks(self):
        """Setup Dash callbacks for interactivity."""
        @self.app.callback(
            Output('detail-panel', 'children'),
            [Input('network-graph', 'clickData')]
        )
        def update_details(clickData):
            if not clickData:
                return self.get_default_details()
            
            # Get clicked node
            point = clickData['points'][0]
            node_id = point['customdata']
            node_info = self.node_info.get(node_id, {})
            
            return self.format_node_details(node_id, node_info)

    def get_default_details(self):
        """Get default detail panel content."""
        return html.Div([
            html.H3("🎯 Welcome to the Interactive Data Visualizer",
                   style={'color': '#2C3E50', 'textAlign': 'center'}),
            html.P("Click on any node in the network graph above to see detailed information.",
                   style={'textAlign': 'center', 'fontSize': 16, 'color': '#7F8C8D'}),
            html.Div([
                html.Div("👤", style={'fontSize': 30, 'textAlign': 'center'}),
                html.P("User Nodes", style={'fontWeight': 'bold', 'textAlign': 'center'}),
                html.P("Red circles representing users", style={'textAlign': 'center', 'fontSize': 14})
            ], style={'width': '30%', 'display': 'inline-block', 'margin': '20px 1.5%'}),

            html.Div([
                html.Div("📅", style={'fontSize': 30, 'textAlign': 'center'}),
                html.P("Appointment Nodes", style={'fontWeight': 'bold', 'textAlign': 'center'}),
                html.P("Teal circles with appointment data", style={'textAlign': 'center', 'fontSize': 14})
            ], style={'width': '30%', 'display': 'inline-block', 'margin': '20px 1.5%'}),

            html.Div([
                html.Div("⚙️", style={'fontSize': 30, 'textAlign': 'center'}),
                html.P("Workflow Nodes", style={'fontWeight': 'bold', 'textAlign': 'center'}),
                html.P("Blue circles with workflow information", style={'textAlign': 'center', 'fontSize': 14})
            ], style={'width': '30%', 'display': 'inline-block', 'margin': '20px 1.5%'})
        ])

    def format_node_details(self, node_id, node_info):
        """Format detailed information for a clicked node."""
        if not node_info:
            return html.Div("No information available for this node.")

        node_type = node_info.get('type', 'unknown')
        data = node_info.get('data', {})

        if node_type == 'user':
            return self.format_user_details(data)
        elif node_type == 'appointment':
            return self.format_appointment_details(data)
        elif node_type == 'workflow':
            return self.format_workflow_details(data)
        else:
            return html.Div("Unknown node type.")

    def format_user_details(self, user_data):
        """Format user details."""
        return html.Div([
            html.H3("👤 User Information", style={'color': '#E74C3C', 'borderBottom': '2px solid #E74C3C', 'paddingBottom': 10}),
            html.Div([
                html.Div([
                    html.H4("Basic Information", style={'color': '#2C3E50'}),
                    html.P(f"Name: {user_data.get('Firstname', '')} {user_data.get('Lastname', '')}", style={'margin': '5px 0'}),
                    html.P(f"User ID: {user_data.get('UserID', 'N/A')}", style={'margin': '5px 0'}),
                    html.P(f"Email: {user_data.get('Email', 'N/A')}", style={'margin': '5px 0'}),
                    html.P(f"Phone: {user_data.get('Phone', 'N/A')}", style={'margin': '5px 0'})
                ], style={'width': '48%', 'display': 'inline-block', 'verticalAlign': 'top'}),

                html.Div([
                    html.H4("Additional Details", style={'color': '#2C3E50'}),
                    *[html.P(f"{k}: {v}", style={'margin': '5px 0'})
                      for k, v in user_data.items()
                      if k not in ['Firstname', 'Lastname', 'UserID', 'Email', 'Phone'] and v is not None]
                ], style={'width': '48%', 'display': 'inline-block', 'verticalAlign': 'top', 'marginLeft': '4%'})
            ])
        ])

    def format_appointment_details(self, appt_data):
        """Format appointment details."""
        appt = appt_data.get('Appointment', {})
        support_data = appt_data.get('Appointments_Support', [])
        outbound_data = appt_data.get('OutBound_Document', [])
        workflows = appt_data.get('Workflows', [])

        return html.Div([
            html.H3(f"📅 Appointment {appt.get('AppNo', 'N/A')}",
                   style={'color': '#27AE60', 'borderBottom': '2px solid #27AE60', 'paddingBottom': 10}),

            # Main appointment info
            html.Div([
                html.H4("📋 Main Information", style={'color': '#2C3E50'}),
                html.Div([
                    html.P(f"{k}: {self.format_datetime(v) if 'DateTime' in k or 'Date' in k else v}",
                          style={'margin': '5px 0'})
                    for k, v in appt.items() if v is not None
                ], style={'backgroundColor': '#E8F8F5', 'padding': 15, 'borderRadius': 8, 'marginBottom': 20})
            ]),

            # Support records
            html.Div([
                html.H4(f"🛠️ Support Records ({len(support_data)})", style={'color': '#2C3E50'}),
                html.Div([
                    html.Div([
                        html.H5(f"Support Record {i+1}", style={'color': '#8E44AD', 'marginBottom': 10}),
                        html.Div([
                            html.P(f"{k}: {v}", style={'margin': '3px 0', 'fontSize': 14})
                            for k, v in support.items() if v is not None
                        ], style={'backgroundColor': '#F8F9FA', 'padding': 10, 'borderRadius': 5, 'marginBottom': 10})
                    ]) for i, support in enumerate(support_data[:3])  # Show first 3
                ] + ([html.P(f"... and {len(support_data)-3} more support records",
                            style={'fontStyle': 'italic', 'color': '#7F8C8D'})] if len(support_data) > 3 else []))
            ], style={'marginBottom': 20}),

            # OutBound documents
            html.Div([
                html.H4(f"📄 OutBound Documents ({len(outbound_data)})", style={'color': '#2C3E50'}),
                html.Div([
                    html.Div([
                        html.H5(f"Document {i+1}", style={'color': '#E67E22', 'marginBottom': 10}),
                        html.Div([
                            html.P(f"{k}: {v}", style={'margin': '3px 0', 'fontSize': 14})
                            for k, v in doc.items() if v is not None
                        ], style={'backgroundColor': '#FDF2E9', 'padding': 10, 'borderRadius': 5, 'marginBottom': 10})
                    ]) for i, doc in enumerate(outbound_data[:3])  # Show first 3
                ] + ([html.P(f"... and {len(outbound_data)-3} more documents",
                            style={'fontStyle': 'italic', 'color': '#7F8C8D'})] if len(outbound_data) > 3 else []))
            ], style={'marginBottom': 20}),

            # Workflows summary
            html.Div([
                html.H4(f"⚙️ Workflows ({len(workflows)})", style={'color': '#2C3E50'}),
                html.Div([
                    html.Div([
                        html.P(f"Workflow {wf.get('AppointmentId', 'N/A')}: "
                              f"{len(wf.get('WorkflowAuditLog', []))} audits, "
                              f"{len(wf.get('KioskWorkflow', []))} kiosk records",
                              style={'margin': '5px 0', 'padding': 10, 'backgroundColor': '#EBF5FB',
                                    'borderRadius': 5, 'border': '1px solid #3498DB'})
                    ]) for wf in workflows
                ])
            ])
        ])

    def format_workflow_details(self, workflow_data):
        """Format workflow details."""
        audit_logs = workflow_data.get('WorkflowAuditLog', [])
        kiosk_workflows = workflow_data.get('KioskWorkflow', [])

        return html.Div([
            html.H3(f"⚙️ Workflow {workflow_data.get('AppointmentId', 'N/A')}",
                   style={'color': '#3498DB', 'borderBottom': '2px solid #3498DB', 'paddingBottom': 10}),

            # Audit logs
            html.Div([
                html.H4(f"📊 Audit Logs ({len(audit_logs)})", style={'color': '#2C3E50'}),
                html.Div([
                    html.Div([
                        html.H5(f"Audit Log {i+1}", style={'color': '#E67E22', 'marginBottom': 10}),
                        html.Div([
                            html.P(f"{k}: {self.format_datetime(v) if 'DateTime' in k else v}",
                                  style={'margin': '3px 0', 'fontSize': 14})
                            for k, v in audit.items() if v is not None
                        ], style={'backgroundColor': '#FDF2E9', 'padding': 10, 'borderRadius': 5, 'marginBottom': 10})
                    ]) for i, audit in enumerate(audit_logs[:2])  # Show first 2
                ] + ([html.P(f"... and {len(audit_logs)-2} more audit logs",
                            style={'fontStyle': 'italic', 'color': '#7F8C8D'})] if len(audit_logs) > 2 else []))
            ], style={'marginBottom': 20}),

            # Kiosk workflows
            html.Div([
                html.H4(f"🖥️ Kiosk Workflows ({len(kiosk_workflows)})", style={'color': '#2C3E50'}),
                html.Div([
                    html.Div([
                        html.H5(f"Kiosk Workflow {i+1}", style={'color': '#8E44AD', 'marginBottom': 10}),
                        html.Div([
                            html.P(f"{k}: {self.format_datetime(v) if 'DateTime' in k or 'Date' in k else v}",
                                  style={'margin': '3px 0', 'fontSize': 14})
                            for k, v in kiosk.items() if v is not None
                        ], style={'backgroundColor': '#F4ECF7', 'padding': 10, 'borderRadius': 5, 'marginBottom': 10})
                    ]) for i, kiosk in enumerate(kiosk_workflows[:2])  # Show first 2
                ] + ([html.P(f"... and {len(kiosk_workflows)-2} more kiosk workflows",
                            style={'fontStyle': 'italic', 'color': '#7F8C8D'})] if len(kiosk_workflows) > 2 else []))
            ])
        ])

    def run(self, debug=False, port=8050):
        """Run the Dash app."""
        print(f"🚀 Starting Advanced Plotly + Dash Visualizer...")
        print(f"🌐 Opening at: http://localhost:{port}")
        print(f"💡 Click on nodes in the network to see detailed information!")
        print(f"📊 Scroll down to see analytics charts!")
        print("=" * 60)

        self.app.run(debug=debug, port=port, host='0.0.0.0')


def main():
    """Main function to run the visualizer."""
    if len(sys.argv) < 2:
        print("❌ Usage: python plotly_dash_visualizer.py <json_file>")
        print("💡 Example: python plotly_dash_visualizer.py Bush_George.json")
        return

    json_file = sys.argv[1]

    try:
        visualizer = PlotlyDashVisualizer(json_file)
        visualizer.run(debug=False)
    except FileNotFoundError:
        print(f"❌ Error: File '{json_file}' not found.")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
