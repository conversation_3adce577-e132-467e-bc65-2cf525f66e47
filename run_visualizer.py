"""
Simple launcher script for the Interactive Data Visualizer
This script helps you run the visualization with your JSON data.
"""

import os
import sys
import subprocess
from pathlib import Path


def find_json_files():
    """Find all JSON files in the current directory."""
    json_files = list(Path('.').glob('*.json'))
    return [str(f) for f in json_files]


def main():
    print("🎯 Interactive Data Visualizer Launcher")
    print("=" * 50)
    
    # Find JSON files
    json_files = find_json_files()
    
    if not json_files:
        print("❌ No JSON files found in the current directory.")
        print("💡 First, run your testdb.py script to generate a JSON file:")
        print("   python testdb.py <FirstName> <LastName>")
        return
    
    print(f"📁 Found {len(json_files)} JSON file(s):")
    for i, file in enumerate(json_files, 1):
        print(f"   {i}. {file}")
    
    # Let user choose a file
    if len(json_files) == 1:
        selected_file = json_files[0]
        print(f"\n🎯 Using: {selected_file}")
    else:
        try:
            choice = input(f"\n🔢 Select a file (1-{len(json_files)}): ")
            selected_file = json_files[int(choice) - 1]
        except (ValueError, IndexError):
            print("❌ Invalid selection. Using the first file.")
            selected_file = json_files[0]
    
    # Check if required packages are installed
    try:
        import streamlit
        print("✅ Streamlit is installed.")
    except ImportError:
        print("❌ Streamlit not found.")
        print("💡 Install it with: pip install streamlit")
        return

    # Run the visualizer
    print(f"\n🚀 Starting Streamlit Data Visualizer...")
    print(f"📊 Loading data from: {selected_file}")
    print(f"🌐 The visualization will open in your browser automatically")
    print(f"💡 Use the expandable sections to see detailed information!")
    print("\n" + "=" * 50)

    try:
        subprocess.run([sys.executable, "-m", "streamlit", "run", "streamlit_visualizer.py", selected_file])
    except KeyboardInterrupt:
        print("\n👋 Visualization stopped.")
    except Exception as e:
        print(f"❌ Error running visualizer: {e}")


if __name__ == "__main__":
    main()
