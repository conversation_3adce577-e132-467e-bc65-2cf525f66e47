"""
Streamlit Interactive Data Visualizer for JSON Data
Creates a hierarchical tree view with expandable sections for detailed information.
"""

import json
import sys
import streamlit as st
from datetime import datetime
from pathlib import Path


class StreamlitDataVisualizer:
    def __init__(self, json_file_path: str):
        """Initialize the visualizer with JSON data."""
        with open(json_file_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
    
    def format_datetime(self, dt_str):
        """Format datetime strings for display."""
        if not dt_str:
            return ""
        try:
            if isinstance(dt_str, str):
                dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
                return dt.strftime("%d-%b-%Y %H:%M")
            return str(dt_str)
        except:
            return str(dt_str)
    
    def get_preview_fields(self, data_dict, max_fields=2):
        """Get a preview of the first few fields from a dictionary."""
        if not data_dict:
            return ""
        
        items = list(data_dict.items())[:max_fields]
        preview = ', '.join(f"{k}={v}" for k, v in items if v is not None)
        suffix = " ..." if len(data_dict) > max_fields else ""
        return preview + suffix
    
    def render_tree_view(self):
        """Render the main tree view."""
        st.title("📊 Interactive Data Visualizer")
        
        # User information
        user = self.data.get('User', {})
        user_name = f"{user.get('Firstname', '')} {user.get('Lastname', '')}".strip()
        
        if user_name:
            st.header(f"👤 User: {user_name}")
        else:
            st.header("👤 User Information")
        
        # User details in expandable section
        with st.expander("🔍 View User Details", expanded=False):
            if user:
                for key, value in user.items():
                    if value is not None:
                        st.write(f"**{key}:** {value}")
            else:
                st.write("No user data available")
        
        # Summary statistics
        self.render_summary_stats()
        
        # Appointments tree
        st.header("🌳 Appointment Hierarchy")
        appointments = self.data.get('Appointments', [])
        
        if not appointments:
            st.warning("No appointments found in the data.")
            return
        
        for i, appt_data in enumerate(appointments):
            self.render_appointment_node(appt_data, i)
    
    def render_summary_stats(self):
        """Render summary statistics."""
        appointments = self.data.get('Appointments', [])
        n_appt = len(appointments)
        n_sup = sum(len(a.get('Appointments_Support', [])) for a in appointments)
        n_outbound = sum(len(a.get('OutBound_Document', [])) for a in appointments)
        n_wf = sum(len(a.get('Workflows', [])) for a in appointments)
        n_audit = sum(sum(len(wf.get('WorkflowAuditLog', [])) for wf in a.get('Workflows', [])) for a in appointments)
        n_kiosk = sum(sum(len(wf.get('KioskWorkflow', [])) for wf in a.get('Workflows', [])) for a in appointments)
        
        st.subheader("📈 Data Summary")
        
        col1, col2, col3, col4, col5, col6 = st.columns(6)
        
        with col1:
            st.metric("Appointments", n_appt)
        with col2:
            st.metric("Support Records", n_sup)
        with col3:
            st.metric("OutBound Docs", n_outbound)
        with col4:
            st.metric("Workflows", n_wf)
        with col5:
            st.metric("Audit Logs", n_audit)
        with col6:
            st.metric("Kiosk Records", n_kiosk)
        
        st.divider()
    
    def render_appointment_node(self, appt_data, index):
        """Render a single appointment node with its hierarchy."""
        appt = appt_data.get('Appointment', {})
        appt_id = appt.get('AppointmentId') or appt.get('AppId') or appt.get('ID')
        appt_no = appt.get('AppointmentNo') or appt.get('AppNo')
        dt_val = (
            appt.get('AppointmentDateTime') or 
            appt.get('ApptDateTime') or 
            appt.get('StartTime') or 
            appt.get('DateTime') or ""
        )
        
        # Main appointment header
        appt_header = f"├─ **Appointment {appt_id}**  (AppNo {appt_no}, {self.format_datetime(dt_val)})"
        st.markdown(appt_header)
        
        # Appointment details in expandable section
        with st.expander(f"🔍 View Appointment {appt_id} Details", expanded=False):
            st.subheader("Main Appointment Information")
            for key, value in appt.items():
                if value is not None:
                    st.write(f"**{key}:** {value}")
        
        # Support records
        support_data = appt_data.get('Appointments_Support', [])
        n_sup = len(support_data)
        st.markdown(f"│   ├─ **{n_sup} Appointments_Support row{'s' if n_sup != 1 else ''}**")
        
        for i, sup in enumerate(support_data[:2]):  # Show first 2
            preview = self.get_preview_fields(sup, 2)
            st.markdown(f"│   │   • {preview}")
        
        if n_sup > 2:
            st.markdown(f"│   │   ... ({n_sup-2} more)")
        
        # Support details in expandable section
        if support_data:
            with st.expander(f"🔍 View All {n_sup} Support Record{'s' if n_sup != 1 else ''}", expanded=False):
                for i, sup in enumerate(support_data):
                    st.subheader(f"Support Record {i+1}")
                    for key, value in sup.items():
                        if value is not None:
                            st.write(f"**{key}:** {value}")
                    if i < len(support_data) - 1:
                        st.divider()
        
        # OutBound documents
        outbound_data = appt_data.get('OutBound_Document', [])
        n_outbound = len(outbound_data)
        st.markdown(f"│   ├─ **{n_outbound} OutBound_Document row{'s' if n_outbound != 1 else ''}**")
        
        for i, ob in enumerate(outbound_data[:2]):  # Show first 2
            preview = self.get_preview_fields(ob, 2)
            st.markdown(f"│   │   • {preview}")
        
        if n_outbound > 2:
            st.markdown(f"│   │   ... ({n_outbound-2} more)")
        
        # OutBound details in expandable section
        if outbound_data:
            with st.expander(f"🔍 View All {n_outbound} OutBound Document{'s' if n_outbound != 1 else ''}", expanded=False):
                for i, doc in enumerate(outbound_data):
                    st.subheader(f"OutBound Document {i+1}")
                    for key, value in doc.items():
                        if value is not None:
                            st.write(f"**{key}:** {value}")
                    if i < len(outbound_data) - 1:
                        st.divider()
        
        # Workflows
        workflows = appt_data.get('Workflows', [])
        for j, wf in enumerate(workflows):
            wf_id = wf.get('AppointmentId')
            wf_prefix = "│   ├─" if j < len(workflows) - 1 else "│   └─"
            st.markdown(f"{wf_prefix} **Workflow {wf_id}**")
            
            # Audit logs
            audit_logs = wf.get('WorkflowAuditLog', [])
            n_audit = len(audit_logs)
            st.markdown(f"│   │   ├─ **{n_audit} WorkflowAuditLog row{'s' if n_audit != 1 else ''}**")
            
            for k, audit in enumerate(audit_logs[:2]):  # Show first 2
                preview = self.get_preview_fields(audit, 2)
                st.markdown(f"│   │   │   • {preview}")
            
            if n_audit > 2:
                st.markdown(f"│   │   │   ... ({n_audit-2} more)")
            
            # Audit details in expandable section
            if audit_logs:
                with st.expander(f"🔍 View All {n_audit} Audit Log{'s' if n_audit != 1 else ''} for Workflow {wf_id}", expanded=False):
                    for i, audit in enumerate(audit_logs):
                        st.subheader(f"Audit Log {i+1}")
                        for key, value in audit.items():
                            if value is not None:
                                if 'DateTime' in key:
                                    st.write(f"**{key}:** {self.format_datetime(value)}")
                                else:
                                    st.write(f"**{key}:** {value}")
                        if i < len(audit_logs) - 1:
                            st.divider()
            
            # Kiosk workflows
            kiosk_rows = wf.get('KioskWorkflow', [])
            kiosk_ids = [kw.get('KioskWorkflowId') for kw in kiosk_rows if kw.get('KioskWorkflowId')]
            kiosk_ids_str = f"   (IDs {', '.join(map(str, kiosk_ids))})" if kiosk_ids else ""
            
            st.markdown(f"│   │   └─ **{len(kiosk_rows)} KioskWorkflow row{'s' if len(kiosk_rows) != 1 else ''}**{kiosk_ids_str}")
            
            # Kiosk details in expandable section
            if kiosk_rows:
                with st.expander(f"🔍 View All {len(kiosk_rows)} Kiosk Workflow{'s' if len(kiosk_rows) != 1 else ''}", expanded=False):
                    for i, kiosk in enumerate(kiosk_rows):
                        st.subheader(f"Kiosk Workflow {i+1}")
                        for key, value in kiosk.items():
                            if value is not None:
                                if 'DateTime' in key or 'Date' in key:
                                    st.write(f"**{key}:** {self.format_datetime(value)}")
                                else:
                                    st.write(f"**{key}:** {value}")
                        if i < len(kiosk_rows) - 1:
                            st.divider()
        
        # Add spacing between appointments
        st.markdown("")
    
    def run(self):
        """Run the Streamlit app."""
        st.set_page_config(
            page_title="Interactive Data Visualizer",
            page_icon="📊",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # Sidebar with file selection
        st.sidebar.title("📁 Data File")
        
        # Show current file info
        if hasattr(self, 'current_file'):
            st.sidebar.success(f"Loaded: {self.current_file}")
        
        # File statistics in sidebar
        appointments = self.data.get('Appointments', [])
        user = self.data.get('User', {})
        
        st.sidebar.subheader("📊 Quick Stats")
        st.sidebar.write(f"**User:** {user.get('Firstname', '')} {user.get('Lastname', '')}")
        st.sidebar.write(f"**Appointments:** {len(appointments)}")
        st.sidebar.write(f"**Extracted:** {self.data.get('ExtractedAt', 'Unknown')}")
        
        # Main content
        self.render_tree_view()


def main():
    """Main function to run the visualizer."""
    st.set_page_config(
        page_title="Interactive Data Visualizer",
        page_icon="📊",
        layout="wide"
    )
    
    # Check for command line argument
    if len(sys.argv) > 1:
        json_file = sys.argv[1]
    else:
        # Look for JSON files in current directory
        json_files = list(Path('.').glob('*.json'))
        
        if not json_files:
            st.error("❌ No JSON files found in the current directory.")
            st.info("💡 First, run your testdb.py script to generate a JSON file:")
            st.code("python testdb.py <FirstName> <LastName>")
            return
        
        # Use the first JSON file found
        json_file = str(json_files[0])
        if len(json_files) > 1:
            st.sidebar.warning(f"Multiple JSON files found. Using: {json_file}")
    
    try:
        visualizer = StreamlitDataVisualizer(json_file)
        visualizer.current_file = json_file
        visualizer.run()
    except FileNotFoundError:
        st.error(f"❌ Error: File '{json_file}' not found.")
        st.info("Make sure to run testdb.py first to generate the JSON file.")
    except Exception as e:
        st.error(f"❌ Error: {e}")
        st.exception(e)


if __name__ == "__main__":
    main()
